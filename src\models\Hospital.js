import mongoose from "mongoose";
import { LANGUAGES,HOSPITAL_TYPES ,HOSPITAL_DEFAULT_DURATION} from "../../config/utils/variables";
import {copyTime} from "../helpers/dates";

const Schema = mongoose.Schema;

const HospitalSchema = new mongoose.Schema({
    name: {
        type: String
    },
    address: {
        type: String,
        default: ''
    },
    phoneNumbers: [{
        type: String,
        default: ''
    }],
    localPhone:{type:String},
    fax:{type:String},
    phoneNumber:{type:String},
    type: {
        type: String,
    },
    schedules:[{
        day:Number,startTime:String,endTime:String,startBreak:String,endBreak:String
    }],
    sessions:[{
        type: Schema.Types.ObjectId,
        ref: 'Supply'
    }],
    startTime: {
        type: Date,
        default: new Date('2000-01-01T08:00:00')
    },
    startBreak: {
        type: Date,
        default: new Date('2000-01-01T13:00:00')
    },
    endBreak: {
        type: Date,
        default: new Date('2000-01-01T14:00:00')
    },
    endTime: {
        type: Date,
        default: new Date('2000-01-01T19:00:00')
    },
    doctors: [{
        type: Schema.Types.ObjectId,
        ref: 'Staff'
    }],
    receptionists: [{
        type: Schema.Types.ObjectId,
        ref: 'Staff'
    }],
    avgSessionDuration:{ type : Number },
    language: {
        type: String,
        default: 'fr',
        enum: LANGUAGES,
    },
    currency: {
        type: String,
        default: 'MAD'
    },
    country: {
        type: Schema.Types.ObjectId,
        ref: 'Country'
    },
    isManager: {
        type: Boolean,
        enum: false
    },
    code: {
        type: String
    },
    city: {
        type: Schema.Types.ObjectId,
        ref: 'City'
    },
    address2: {
        type: String
    },
    address3: {
        type: String
    },
    email: {
        type: String
    },
    tenant: {
        type: Schema.Types.ObjectId,
        ref: 'Tenant'
    },
    prescriptionHeader: {
        type: Boolean
    },
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
HospitalSchema.pre('find', populateHospital);
HospitalSchema.pre('findOne', populateHospital);
HospitalSchema.pre('findOneAndUpdate', populateHospital);
HospitalSchema.statics.softDelete = function (query, callback) {
    return this.findOneAndUpdate(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};
function populateHospital(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    this.populate({
        path: 'doctors',
        populate: {
            path:"profile",
            select: "-staff"
        }
    }).populate('receptionists', "-specialty profilePic firstName lastName title").populate("sessions","name sellingPrice avgDuration").populate("tenant").populate("city").populate("country");
    next();
}

  function defaultSchedules() {
    return [1,2,3,4,5,6,0].map(x=>{
        let startTime='08:00';
        let endTime='19:00';
        let startBreak='13:00';
        let endBreak='08:00';
        return {day:x,startTime,endTime,startBreak,endBreak}
    });
  }

module.exports = mongoose.model("Hospital", HospitalSchema);
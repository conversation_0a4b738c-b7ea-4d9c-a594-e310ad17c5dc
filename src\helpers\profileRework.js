import {STAFF_TITLES} from "../../config/utils/variables";

export const restructureProfileObj = (origin , isProfile) => {
    let profile, t, p;

    const title = isProfile ? origin.title : origin.profile.title ;
    const isStaff = STAFF_TITLES.includes(title) 
    const isPatient = title == "PATIENT";
    const isSupplier = title == "SUPPLIER";
    const isSuperAdmin = title == "SUPER_ADMIN";

    if(isProfile){
        if(isStaff){
            t = origin.staff && origin.staff._doc ? origin.staff._doc : origin.staff;
        }else if(isPatient){
            t =  origin.patient && origin.patient._doc ? origin.patient._doc : origin.patient;
        }else if(isSupplier){
            t = origin.supplier && origin.supplier._doc ? origin.supplier._doc : origin.supplier;
        }else if(isSuperAdmin){
            t = origin.superAdmin && origin.superAdmin._doc ? origin.superAdmin._doc : origin.superAdmin;
        }
        p = origin._doc ? origin._doc : origin;
    }else{
        p = origin.profile && origin.profile._doc ? origin.profile._doc : origin.profile;
        t = origin._doc ? origin._doc : origin; 
    }

    if(t){
        if(isStaff){
            t.staffId = t._id 
        }else if(isPatient){
            t.patientId = t._id
        }else if(isSupplier){
            t.supplierId = t._id
        }else if(isSuperAdmin){
            t.superAdminId = t._id
        }
    }

    profile = Object.assign({},t,p)

    delete profile.profile 
    delete profile.staff;
    delete profile.patient;
    delete profile.supplier;
    delete profile.superAdmin;

    return profile;
}

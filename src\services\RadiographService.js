import Service from './Service';
import APIError from '../errors/APIError';
import mongoose from 'mongoose';
import Radiofamily from "../models/RadiographFamily";
import Hospital from '../models/Hospital';

class RadiographService extends Service {
    constructor(model) {
        super(model);
        this.getRadios = this.getRadios.bind(this);
        this.createOrEditRadio = this.createOrEditRadio.bind(this);
        this.getRadioFamilies = this.getRadioFamilies.bind(this);
        this.createOrEditRadioFamily = this.createOrEditRadioFamily.bind(this);

        this.queryRadio = this.queryRadio.bind(this);
        this.queryRadioFamily = this.queryRadioFamily.bind(this);
    }

    async getRadios(filters , user) {
        const managerHospitals = await Hospital.find({isManager: true});
        const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
        let query={hospital: {$in: hospitalIds}};
        query=await this.queryRadio(query,filters,user);
        let options = {
            sort: {type:-1,name:1,createdAt:1 },
            page: parseInt(filters.page, 10) || 1,
            limit: parseInt(filters.limit, 10) || 1000
        };
        let radios = await this.model.find(query);
        return radios;
    }

    async createOrEditRadio(radio , user) {
        let radioId = radio._id;
        radio.hospital=user.profile.hospital._id;
        radio.updatedBy=user.profile._id;
        if(!radioId) radio.createdBy=user.profile._id;
        let query={_id : mongoose.Types.ObjectId(radioId) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        radio = await this.model.findOneAndUpdate(query,radio   ,{new:true,upsert: true, setDefaultsOnInsert:true});
        if (!radio) throw new APIError(404, 'cannot create Radio');
        return radio
    }

    async getRadioFamilies(filters , user) {
        const managerHospitals = await Hospital.find({isManager: true});
        const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
        let query={hospital: {$in: hospitalIds}};
        query=await this.queryRadioFamily(query,filters,user);
        let options = {
            sort: {type:-1,name:1,createdAt:1 },
            page: parseInt(filters.page, 10) || 1,
            limit: parseInt(filters.limit, 10) || 1000
        };
        let radioFamilies = await Radiofamily.find(query);
        return radioFamilies;
    }

    async createOrEditRadioFamily(radioFamily , user) {
        let radioFamilyID = radioFamily._id;
        radioFamily.hospital=user.profile.hospital._id;
        radioFamily.updatedBy=user.profile._id;
        if(!radioFamilyID) radioFamily.createdBy=user.profile._id;
        let query={_id : mongoose.Types.ObjectId(radioFamilyID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        radioFamily = await Radiofamily.findOneAndUpdate(query,radioFamily   ,{new:true,upsert: true, setDefaultsOnInsert:true});
        if (!radioFamily) throw new APIError(404, 'cannot create RadioFamily');
        return radioFamily
    }

    async queryRadioFamily(query={},filters, user) {
        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [
                { name: { $regex: '^' +filters.searchText, $options: "i" } }, 
            ];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
            query["$and"].push({ $or: or });
        }
        return query;
    }

    async queryRadio(query={},filters, user) {
        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [
                { name: { $regex: '^' +filters.searchText, $options: "i" } }, 
            ];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
            query["$and"].push({ $or: or });
        }
        return query;
    }
}

export default RadiographService;
<div
  fxLayout="row"
  fxLayoutAlign="space-around center"
  class="rend-as"
  [ngClass]="{ 'mt-4': !isFirstProduct }"
  [dir]="dir"
>
  <div fxFlex="20">
    <h3 [dir]="dir" class="titre-time ml-2">{{ product.name }}</h3>
  </div>




  <div class="stat-rendez" fxFlex="10">
    <div class="btn-group">
      <app-circle-button
        name="edit"
        [matTooltip]="'supplies.tooltips.edit' | translate"
        (click)="updateClick(product)"
      ></app-circle-button>
      <app-circle-button
        name="delete"
        [matTooltip]="'supplies.tooltips.delete' | translate"
        (click)="deleteClick(product)"
      ></app-circle-button>
    </div>
  </div>
</div>

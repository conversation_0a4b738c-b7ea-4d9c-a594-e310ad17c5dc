import Service from './Service';
import APIError from '../errors/APIError';
import mongoose from 'mongoose';
import Hospital from '../models/Hospital';

class BiologieService extends Service {
    constructor(model) {
        super(model);
        this.getBios = this.getBios.bind(this);
        this.createOrEditBio = this.createOrEditBio.bind(this);
        this.queryBio = this.queryBio.bind(this);
    }

    async getBios(filters , user) {
        const managerHospitals = await Hospital.find({isManager: true});
        const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
        let query={hospital: {$in: hospitalIds}};
        query=await this.queryBio(query,filters,user);
        let options = {
            sort: {type:-1,name:1,createdAt:1 },
            page: parseInt(filters.page, 10) || 1,
            limit: parseInt(filters.limit, 10) || 1000
        };
        let bios = await this.model.find(query);
        return bios;
    }

    async createOrEditBio(bio , user) {
        let bioID = bio._id;
        bio.hospital=user.profile.hospital._id;
        bio.updatedBy=user.profile._id;
        if(!bioID) bio.createdBy=user.profile._id;
        let query={_id : mongoose.Types.ObjectId(bioID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        bio = await this.model.findOneAndUpdate(query,bio   ,{new:true,upsert: true, setDefaultsOnInsert:true});
        if (!bio) throw new APIError(404, 'cannot create Bilogie');
        return bio
    }

    async queryBio(query={},filters, user) {
        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [
                { name: { $regex: '^' +filters.searchText, $options: "i" } }, 
            ];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
            query["$and"].push({ $or: or });
        }
        return query;
    }

}

export default BiologieService;
{"name": "winmed-backend", "version": "0.1.12", "description": "", "main": "./app.js", "scripts": {"start": "cross-env NODE_ENV=dev nodemon --ignore swagger_specs.json --exec babel-node app.js", "start:production": "NODE_ENV=production node app.js", "build": " npx shx rm -r ./build && babel ./ --out-dir ./build --ignore ./node_modules,ba./bin,./.belrc,./web.config,./npm-debug.log --copy-files && npx shx cp -r environments ./build/environments", "initdb": "mongorestore --drop -d winmed_office test/db/winmed_office/", "updatedb": "mongodump -d winmed_office -o test/db/", "lint": "eslint ./", "populateDB": "nodemon --exec babel-node ./scripts/initializeDbDev.js", "populateDBProd": "NODE_ENV=production node ./scripts/initializeDbDev.js", "populateMH": "nodemon --exec babel-node ./scripts/initDbManagerHospital.js", "populateMHProd": "NODE_ENV=production node ./scripts/initDbManagerHospital.js"}, "keywords": ["ES", "MODULES", "NODE", "MODULES", "JS"], "author": "moorsmed", "license": "ISC", "dependencies": {"@babel/core": "^7.6.0", "@babel/node": "^7.6.1", "@babel/preset-env": "^7.6.0", "@babel/preset-react": "^7.9.4", "azure-storage": "^2.10.3", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "boom": "^7.3.0", "buffer-to-uint8array": "^1.1.0", "compression": "^1.7.4", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "cross-blob": "^2.0.0", "crypto-js": "^4.0.0", "crypto-ts": "^1.0.2", "csv-parser": "^3.2.0", "dotenv": "^8.2.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "exceljs": "^4.2.1", "express": "^4.17.1", "express-jsonschema": "^1.1.6", "express-mongo-sanitize": "^2.0.0", "express-oas-generator": "^1.0.46", "file-system": "^2.2.2", "handlebars": "^4.7.7", "helmet": "^4.1.0", "http2": "^3.3.7", "https": "^1.0.0", "into-stream": "^6.0.0", "jsonwebtoken": "^8.5.1", "mailgun": "^0.5.0", "mailgun-js": "^0.22.0", "moment": "^2.29.1", "mongoose": "^5.9.24", "mongoose-dummy": "^1.0.8", "mongoose-paginate": "^5.0.3", "mongoose-relationship": "^0.1.5", "mongoose-to-swagger": "^1.5.1", "morgan": "^1.10.0", "multer": "^1.4.2", "node-cache": "^5.1.2", "nodemailer": "^6.8.0", "nodemailer-express-handlebars": "^4.0.0", "nodemailer-mailgun-transport": "^2.0.2", "path": "^0.12.7", "prettier": "^2.1.2", "rand-token": "^1.0.1", "request-ip": "^2.1.3", "socket.io": "^3.1.0", "stream": "0.0.2", "swagger-ui-express": "^5.0.0", "util": "^0.12.3", "uuid": "^8.3.0", "uuid-random": "^1.3.2", "xlsx": "^0.16.9"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "chai": "^4.2.0", "cross-env": "^7.0.3", "eslint": "^7.12.1", "eslint-config-airbnb-base": "^14.2.0", "eslint-plugin-import": "^2.22.1", "nodemon": "^2.0.4", "shx": "^0.3.4"}}
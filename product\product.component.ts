import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Direction} from '@angular/cdk/bidi';
import {ErrorService} from '../../services/error.service';
import {MatDialog} from '@angular/material/dialog';
import {CALLS_TYPES} from '../../constants/defaults.consts';
import {PrescriptionService} from '../../services/prescription.service';
import {DrugDialogComponent} from '../drug-dialog/drug-dialog.component';
import {BiologieDialogComponent} from '../biologie-dialog/biologie-dialog.component';
import {RadiologieDialogComponent} from '../radiologie-dialog/radiologie-dialog.component';
import {MatBottomSheet} from '@angular/material/bottom-sheet';
export type PrescriptionType = 'ORDONNANCE' | 'RADIOLOGIE' | 'BIOLOGIE' | 'AUTRE';

@Component({
  selector: 'app-product',
  templateUrl: './product.component.html',
  styleUrls: ['./product.component.scss']
})
export class ProductComponent implements OnInit {

  @Input() prescriptionType: PrescriptionType = 'ORDONNANCE';
  @Input() isFirstProduct: boolean = false;
  @Input() product: any;
  @Input() dir: Direction = 'ltr';
  @Output() productUpdatedEvent = new EventEmitter<any>();
  @Output() productDeletedEvent = new EventEmitter<any>();

  public isSession: boolean = true;

  constructor(
    private prescriptionService: PrescriptionService,
    private errorService: ErrorService,
    private dialog: MatBottomSheet
  ) {}

  ngOnInit(): void {
    this.checkType();
  }

  checkType() {
    if (this.product.type !== 'SESSION') {
      this.isSession = false;
    }
  }

  updateClick(product: any) {
    let openComp: any = DrugDialogComponent;
    let name: string = 'drug';
    if (this.prescriptionType === 'ORDONNANCE') {
      openComp = DrugDialogComponent;
      name = 'drug';

    } else if (this.prescriptionType === 'BIOLOGIE') {
      openComp = BiologieDialogComponent;
      name = 'bio';
    }
    else if (this.prescriptionType === 'RADIOLOGIE') {
      openComp = RadiologieDialogComponent;
      name = 'radio';
    }
    if (this.product.type === 'SESSION') { return; }
    const dialogRef = this.dialog.open(openComp, {
      data: {
        type: CALLS_TYPES.update,
        [name]: JSON.parse(JSON.stringify(product)),
      },
    });

    dialogRef.afterDismissed().subscribe((result) => {
      if (result) {
        this.productUpdatedEvent.emit(result);
      }
    });
  }
  deleteClick(product: any) {
    if (this.product.type === 'SESSION') { return; }
    this.prescriptionService.deleteProduct(product._id, this.typeTransformer).subscribe((result) => {
      if (result.deletedCount > 0) {
        this.productDeletedEvent.emit(product);
      } else {
        this.errorService.handleError(
          "une erreur s'est produite lors de la suppression de cet élément, veuillez réessayer "
        );
      }
    });
  }
  get typeTransformer() {
    const type = this.prescriptionType;
    if (type === 'ORDONNANCE') { return 'DRUG'; }
    if (type === 'RADIOLOGIE') { return 'RADIOGRAPH'; }
    if (type === 'BIOLOGIE') { return 'BIOLOGIE'; }
    return 'DRUG';
  }

}

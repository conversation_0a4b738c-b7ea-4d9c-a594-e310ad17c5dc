import Service from './Service';
import APIError from '../errors/APIError';
import Drug from '../models/Drug';
import Hospital from '../models/Hospital';
import mongoose from 'mongoose';
import DrugFamily from "../models/DrugFamily";

class DrugService extends Service {
    constructor(model) {
        super(model);
        this.getDrugs = this.getDrugs.bind(this);
        this.createOrEditDrug = this.createOrEditDrug.bind(this);
        this.getDrugFamilies = this.getDrugFamilies.bind(this);
        this.createOrEditDrugFamily = this.createOrEditDrugFamily.bind(this);

        this.queryDrug = this.queryDrug.bind(this);
        this.queryDrugFamily = this.queryDrugFamily.bind(this);
    }

        async getDrugs(filters , user) {
            const managerHospitals = await Hospital.find({isManager: true});
            const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
            let query={hospital: {$in: hospitalIds}};
            query=await this.queryDrug(query,filters,user);
            let options = {
                sort: {name:1,price:1,createdAt:1 },
                page: parseInt(filters.page, 10) || 1,
                limit: parseInt(filters.limit, 10) || 1000,
            };
            let drugs = await this.model.find(query);
            if (!drugs) throw new APIError(404, 'cannot find drugs');
            return drugs;
          }

        async initDrugs() {
            let db=this.model.db;
            let drugs=[];
            //medicaments
            await db.collection('medicaments').find({}).toArray(async function (err,medicaments) {
                medicaments.map(x=>{
                    let drug={};
                let string= x['drug name '];
                if(string && string !== '' && string.length>0){
                drug.name=string.split(',')[0];
                if(string.split(', ')[1]){
                    drug.description=string.split(', ')[1].split('- PPV:')[0].replace(/\s{2,}/g, ' ').replace('\n', '');
                    if(string.split(', ')[1].split('- PPV:')[1])
                drug.price=Number(string.split(', ')[1].split('- PPV:')[1].split(' dhs')[0]);  
                }
                }
                drugs.push(drug)
                
            })
            drugs=await Drug.insertMany(drugs);             
            return drugs;
            })
        }

        async createOrEditDrug(drug , user) {
            let drugID = drug._id;
            drug.hospital=user.profile.hospital._id;
            drug.updatedBy=user.profile._id;
            if(!drugID) drug.createdBy=user.profile._id;
            let query={_id : mongoose.Types.ObjectId(drugID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
            drug = await this.model.findOneAndUpdate(query,drug   ,{new:true,upsert: true, setDefaultsOnInsert:true});
            if (!drug) throw new APIError(404, 'cannot create Drug');
            return drug
        }
    
        async getDrugFamilies(filters , user) {
            const managerHospitals = await Hospital.find({isManager: true});
            const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
            let query={hospital: {$in: hospitalIds}};
            query=await this.queryDrugFamily(query,filters,user);
            let options = {
                sort: {name:1,createdAt:1 },
                page: parseInt(filters.page, 10) || 1,
                limit: parseInt(filters.limit, 10) || 1000
            };
            let drugFamilies = await DrugFamily.find(query);
            return drugFamilies;
        }
    
        async createOrEditDrugFamily(drugFamily , user) {
            let drugFamilyID = drugFamily._id;
            drugFamily.hospital=user.profile.hospital._id;
            drugFamily.updatedBy=user.profile._id;
            if(!drugFamilyID) drugFamily.createdBy=user.profile._id;
            let query={_id : mongoose.Types.ObjectId(drugFamilyID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
            drugFamily = await DrugFamily.findOneAndUpdate(query,drugFamily   ,{new:true,upsert: true, setDefaultsOnInsert:true});
            if (!drugFamily) throw new APIError(404, 'cannot create DrugFamily');
            return drugFamily
        }

        async queryDrug(query={},filters) {
            if (filters.searchText) {
                if (!query["$and"]) query["$and"] = [];
                let or = [
                    { name: { $regex: '^' +filters.searchText, $options: "i" } }
                ];
                if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
                query["$and"].push({ $or: or });
            }
            return query;
        }

        async queryDrugFamily(query={},filters) {
            if (filters.searchText) {
                if (!query["$and"]) query["$and"] = [];
                let or = [
                    { name: { $regex: '^' +filters.searchText, $options: "i" }}, 
                ];
                if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
                query["$and"].push({ $or: or });
            }
            return query;
        }
}

export default DrugService;